import os
import json
import hashlib
import shutil
from typing import List, Dict, Optional, Tuple
from datetime import datetime
import frontmatter
import re
from pathlib import Path

from src.models.note import Note, Tag, ArchivedFile, NoteLink, db, note_tags
from src.services.llm_service import LLMService

class FileService:
    def __init__(self, notes_directory: str = None, llm_service: LLMService = None):
        """
        Initialize file service
        
        Args:
            notes_directory: Directory where active notes are stored
            llm_service: LLM service instance for content processing
        """
        self.notes_directory = notes_directory or os.path.join(os.getcwd(), "notes")
        self.llm_service = llm_service or LLMService()
        
        # Ensure notes directory exists
        os.makedirs(self.notes_directory, exist_ok=True)
    
    def import_file(self, file_path: str, process_with_llm: bool = True) -> Optional[Note]:
        """
        Import a single file (.md or .json) into the system
        
        Args:
            file_path: Path to the file to import
            process_with_llm: Whether to process the file with LLM for topic extraction, etc.
            
        Returns:
            Created Note object or None if import failed
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File not found: {file_path}")
            
            file_extension = os.path.splitext(file_path)[1].lower()
            if file_extension not in ['.md', '.json']:
                raise ValueError(f"Unsupported file type: {file_extension}")
            
            # Read file content
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Archive original file
            archived_file = self._archive_file(file_path, content)
            
            # Copy file to notes directory
            filename = os.path.basename(file_path)
            target_path = os.path.join(self.notes_directory, filename)
            
            # Handle filename conflicts
            counter = 1
            base_name, ext = os.path.splitext(filename)
            while os.path.exists(target_path):
                new_filename = f"{base_name}_{counter}{ext}"
                target_path = os.path.join(self.notes_directory, new_filename)
                counter += 1
            
            shutil.copy2(file_path, target_path)
            
            # Process file content based on type
            if file_extension == '.md':
                note = self._process_markdown_file(target_path, content, process_with_llm)
            else:  # .json
                note = self._process_json_file(target_path, content, process_with_llm)
            
            return note
            
        except Exception as e:
            print(f"Error importing file {file_path}: {e}")
            return None
    
    def import_directory(self, directory_path: str, recursive: bool = True, process_with_llm: bool = True) -> List[Note]:
        """
        Import all supported files from a directory
        
        Args:
            directory_path: Path to the directory to import
            recursive: Whether to search subdirectories
            process_with_llm: Whether to process files with LLM
            
        Returns:
            List of created Note objects
        """
        imported_notes = []
        
        try:
            if recursive:
                for root, dirs, files in os.walk(directory_path):
                    for file in files:
                        if file.lower().endswith(('.md', '.json')):
                            file_path = os.path.join(root, file)
                            note = self.import_file(file_path, process_with_llm)
                            if note:
                                imported_notes.append(note)
            else:
                for file in os.listdir(directory_path):
                    if file.lower().endswith(('.md', '.json')):
                        file_path = os.path.join(directory_path, file)
                        note = self.import_file(file_path, process_with_llm)
                        if note:
                            imported_notes.append(note)
                            
        except Exception as e:
            print(f"Error importing directory {directory_path}: {e}")
        
        return imported_notes
    
    def _process_markdown_file(self, file_path: str, content: str, process_with_llm: bool) -> Note:
        """
        Process a Markdown file and create a Note object
        """
        # Parse frontmatter
        post = frontmatter.loads(content)
        
        # Extract title (from frontmatter or first heading)
        title = post.metadata.get('title')
        if not title:
            # Try to extract from first heading
            lines = post.content.split('\n')
            for line in lines:
                if line.strip().startswith('#'):
                    title = line.strip().lstrip('#').strip()
                    break
        
        if not title:
            title = os.path.splitext(os.path.basename(file_path))[0]
        
        # Create note
        note = Note(
            title=title,
            content=post.content,
            file_path=file_path,
            file_type='md'
        )
        
        db.session.add(note)
        db.session.flush()  # Get the note ID
        
        # Process existing tags from frontmatter
        existing_tags = post.metadata.get('tags', [])
        if isinstance(existing_tags, str):
            existing_tags = [tag.strip() for tag in existing_tags.split(',')]
        
        # Process with LLM if requested
        if process_with_llm and self.llm_service:
            try:
                # Extract topics
                topics = self.llm_service.extract_topics(post.content)
                note.topics = json.dumps(topics)
                
                # Generate summary
                summary = self.llm_service.summarize_content(post.content)
                note.summary = summary
                
                # Generate additional tags
                llm_tags = self.llm_service.generate_tags(post.content, existing_tags)
                existing_tags.extend(llm_tags)
                
            except Exception as e:
                print(f"Error processing with LLM: {e}")
        
        # Add tags to note
        for tag_name in set(existing_tags):  # Remove duplicates
            tag = Tag.query.filter_by(name=tag_name.lower()).first()
            if not tag:
                tag = Tag(name=tag_name.lower())
                db.session.add(tag)
            note.tags.append(tag)
        
        # Extract internal links
        self._extract_internal_links(note, post.content)
        
        db.session.commit()
        return note
    
    def _process_json_file(self, file_path: str, content: str, process_with_llm: bool) -> Note:
        """
        Process a JSON file (e.g., LLM conversation) and create a Note object
        """
        try:
            data = json.loads(content)
            
            # Extract title and content based on JSON structure
            title = data.get('title') or data.get('name') or os.path.splitext(os.path.basename(file_path))[0]
            
            # Convert JSON to readable text for processing
            if 'messages' in data:  # Chat conversation format
                text_content = self._convert_chat_to_text(data['messages'])
            elif 'conversation' in data:
                text_content = self._convert_chat_to_text(data['conversation'])
            else:
                # Generic JSON - convert to formatted text
                text_content = json.dumps(data, indent=2)
            
            # Create note
            note = Note(
                title=title,
                content=text_content,
                file_path=file_path,
                file_type='json'
            )
            
            db.session.add(note)
            db.session.flush()
            
            # Process with LLM if requested
            if process_with_llm and self.llm_service:
                try:
                    # Extract topics
                    topics = self.llm_service.extract_topics(text_content)
                    note.topics = json.dumps(topics)
                    
                    # Generate summary
                    summary = self.llm_service.summarize_content(text_content)
                    note.summary = summary
                    
                    # Generate tags
                    tags = self.llm_service.generate_tags(text_content)
                    
                    # Add tags to note
                    for tag_name in tags:
                        tag = Tag.query.filter_by(name=tag_name.lower()).first()
                        if not tag:
                            tag = Tag(name=tag_name.lower())
                            db.session.add(tag)
                        note.tags.append(tag)
                        
                except Exception as e:
                    print(f"Error processing JSON with LLM: {e}")
            
            db.session.commit()
            return note
            
        except json.JSONDecodeError as e:
            print(f"Error parsing JSON file {file_path}: {e}")
            return None
    
    def _convert_chat_to_text(self, messages: List[Dict]) -> str:
        """
        Convert chat messages to readable text format
        """
        text_parts = []
        
        for message in messages:
            role = message.get('role', 'unknown')
            content = message.get('content', '')
            timestamp = message.get('timestamp', '')
            
            if timestamp:
                text_parts.append(f"[{timestamp}] {role.upper()}: {content}")
            else:
                text_parts.append(f"{role.upper()}: {content}")
        
        return '\n\n'.join(text_parts)
    
    def _extract_internal_links(self, note: Note, content: str):
        """
        Extract internal links from Markdown content and create NoteLink objects
        """
        # Find Obsidian-style links [[Note Name]]
        link_pattern = r'\[\[([^\]]+)\]\]'
        matches = re.findall(link_pattern, content)
        
        for match in matches:
            # Try to find the linked note
            linked_note = Note.query.filter(
                Note.title.ilike(f"%{match}%")
            ).first()
            
            if linked_note and linked_note.id != note.id:
                # Check if link already exists
                existing_link = NoteLink.query.filter_by(
                    from_note_id=note.id,
                    to_note_id=linked_note.id
                ).first()
                
                if not existing_link:
                    link = NoteLink(
                        from_note_id=note.id,
                        to_note_id=linked_note.id,
                        link_type='reference'
                    )
                    db.session.add(link)
    
    def _archive_file(self, file_path: str, content: str) -> ArchivedFile:
        """
        Archive the original file content
        """
        file_hash = hashlib.sha256(content.encode('utf-8')).hexdigest()
        file_size = len(content.encode('utf-8'))
        
        archived_file = ArchivedFile(
            original_path=file_path,
            content=content,
            file_hash=file_hash,
            file_size=file_size
        )
        
        db.session.add(archived_file)
        return archived_file
    
    def update_note_content(self, note_id: int, new_content: str, process_with_llm: bool = True) -> Optional[Note]:
        """
        Update note content and reprocess with LLM if requested
        """
        try:
            note = Note.query.get(note_id)
            if not note:
                return None
            
            # Update content
            note.content = new_content
            note.updated_at = datetime.utcnow()
            
            # Write to file
            with open(note.file_path, 'w', encoding='utf-8') as f:
                if note.file_type == 'md':
                    # Preserve frontmatter if it exists
                    post = frontmatter.Post(new_content)
                    post.metadata['title'] = note.title
                    post.metadata['tags'] = [tag.name for tag in note.tags]
                    f.write(frontmatter.dumps(post))
                else:
                    f.write(new_content)
            
            # Reprocess with LLM if requested
            if process_with_llm and self.llm_service:
                try:
                    topics = self.llm_service.extract_topics(new_content)
                    note.topics = json.dumps(topics)
                    
                    summary = self.llm_service.summarize_content(new_content)
                    note.summary = summary
                    
                except Exception as e:
                    print(f"Error reprocessing with LLM: {e}")
            
            db.session.commit()
            return note
            
        except Exception as e:
            print(f"Error updating note content: {e}")
            return None
    
    def get_notes_directory(self) -> str:
        """
        Get the current notes directory path
        """
        return self.notes_directory
    
    def set_notes_directory(self, directory_path: str):
        """
        Set a new notes directory path
        """
        self.notes_directory = directory_path
        os.makedirs(self.notes_directory, exist_ok=True)

