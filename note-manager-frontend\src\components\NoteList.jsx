import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Search, FileText, Calendar, Tag } from 'lucide-react'
import '../App.css'

const NoteList = ({ onNoteSelect, selectedNoteId }) => {
  const [notes, setNotes] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [filteredNotes, setFilteredNotes] = useState([])

  useEffect(() => {
    fetchNotes()
  }, [])

  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredNotes(notes)
    } else {
      const filtered = notes.filter(note =>
        note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        note.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
        note.summary?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        note.tags.some(tag => tag.name.toLowerCase().includes(searchQuery.toLowerCase()))
      )
      setFilteredNotes(filtered)
    }
  }, [searchQuery, notes])

  const fetchNotes = async () => {
    try {
      const response = await fetch('/api/notes')
      const data = await response.json()
      setNotes(data.notes || [])
      setLoading(false)
    } catch (error) {
      console.error('Error fetching notes:', error)
      setLoading(false)
    }
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const truncateText = (text, maxLength = 150) => {
    if (!text) return ''
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
        <Input
          placeholder="Search notes, content, tags..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Notes Count */}
      <div className="text-sm text-muted-foreground">
        {filteredNotes.length} {filteredNotes.length === 1 ? 'note' : 'notes'}
        {searchQuery && ` matching "${searchQuery}"`}
      </div>

      {/* Notes List */}
      <div className="space-y-3">
        {filteredNotes.map((note) => (
          <Card
            key={note.id}
            className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
              selectedNoteId === note.id ? 'ring-2 ring-primary' : ''
            }`}
            onClick={() => onNoteSelect(note)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg font-semibold line-clamp-1">
                    {note.title}
                  </CardTitle>
                  <CardDescription className="flex items-center gap-2 mt-1">
                    <FileText className="h-3 w-3" />
                    <span className="uppercase text-xs">{note.file_type}</span>
                    <Calendar className="h-3 w-3 ml-2" />
                    <span className="text-xs">
                      {formatDate(note.updated_at)}
                    </span>
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="pt-0">
              {/* Summary */}
              {note.summary && (
                <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                  {truncateText(note.summary)}
                </p>
              )}

              {/* Tags */}
              {note.tags && note.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-3">
                  {note.tags.slice(0, 3).map((tag) => (
                    <Badge
                      key={tag.id}
                      variant="secondary"
                      className="text-xs"
                    >
                      <Tag className="h-2 w-2 mr-1" />
                      {tag.name}
                    </Badge>
                  ))}
                  {note.tags.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{note.tags.length - 3} more
                    </Badge>
                  )}
                </div>
              )}

              {/* Topics */}
              {note.topics && note.topics.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {note.topics.slice(0, 2).map((topic, index) => (
                    <Badge
                      key={index}
                      variant="outline"
                      className="text-xs bg-blue-50 text-blue-700 border-blue-200"
                    >
                      {topic}
                    </Badge>
                  ))}
                  {note.topics.length > 2 && (
                    <Badge variant="outline" className="text-xs">
                      +{note.topics.length - 2} topics
                    </Badge>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredNotes.length === 0 && !loading && (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No notes found</h3>
          <p className="text-muted-foreground">
            {searchQuery
              ? 'Try adjusting your search terms'
              : 'Import some notes to get started'}
          </p>
        </div>
      )}
    </div>
  )
}

export default NoteList

