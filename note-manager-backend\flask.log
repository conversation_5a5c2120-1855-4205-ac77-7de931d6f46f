nohup: ignoring input
 * Serving Flask app 'main'
 * Debug mode: on
INFO:werkzeug:[31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
INFO:werkzeug:[33mPress CTRL+C to quit[0m
INFO:werkzeug: * Restarting with stat
WARNING:werkzeug: * Debugger is active!
INFO:werkzeug: * Debugger PIN: 818-058-068
INFO:werkzeug:127.0.0.1 - - [10/Jul/2025 15:14:05] "GET /api/notes HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [10/Jul/2025 15:15:11] "GET /api/notes?per_page=1 HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [10/Jul/2025 15:15:11] "GET /api/notes HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [10/Jul/2025 15:15:11] "GET /api/notes?per_page=1 HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [10/Jul/2025 15:15:11] "GET /api/notes HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [10/Jul/2025 15:15:12] "GET /api/tags HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [10/Jul/2025 15:15:12] "GET /api/tags HTTP/1.1" 200 -
[92m15:15:12 - LiteLLM:INFO[0m: utils.py:3225 - 
LiteLLM completion() model= llama3.2; provider = ollama
INFO:LiteLLM:
LiteLLM completion() model= llama3.2; provider = ollama
ERROR:src.services.llm_service:LLM connection test failed: litellm.APIConnectionError: OllamaException - [Errno 111] Connection refused
INFO:werkzeug:127.0.0.1 - - [10/Jul/2025 15:15:12] "GET /api/llm/test HTTP/1.1" 200 -
[92m15:15:12 - LiteLLM:INFO[0m: utils.py:3225 - 
LiteLLM completion() model= llama3.2; provider = ollama
INFO:LiteLLM:
LiteLLM completion() model= llama3.2; provider = ollama
ERROR:src.services.llm_service:LLM connection test failed: litellm.APIConnectionError: OllamaException - [Errno 111] Connection refused
INFO:werkzeug:127.0.0.1 - - [10/Jul/2025 15:15:12] "GET /api/llm/test HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [11/Jul/2025 19:25:40] "GET /api/notes?per_page=1 HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [11/Jul/2025 19:25:40] "GET /api/notes HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [11/Jul/2025 19:25:40] "GET /api/notes?per_page=1 HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [11/Jul/2025 19:25:40] "GET /api/notes HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [11/Jul/2025 19:25:40] "GET /api/tags HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [11/Jul/2025 19:25:40] "GET /api/tags HTTP/1.1" 200 -
[92m19:25:41 - LiteLLM:INFO[0m: utils.py:3225 - 
LiteLLM completion() model= llama3.2; provider = ollama
INFO:LiteLLM:
LiteLLM completion() model= llama3.2; provider = ollama
ERROR:src.services.llm_service:LLM connection test failed: litellm.APIConnectionError: OllamaException - [Errno 111] Connection refused
INFO:werkzeug:127.0.0.1 - - [11/Jul/2025 19:25:41] "GET /api/llm/test HTTP/1.1" 200 -
[92m19:25:41 - LiteLLM:INFO[0m: utils.py:3225 - 
LiteLLM completion() model= llama3.2; provider = ollama
INFO:LiteLLM:
LiteLLM completion() model= llama3.2; provider = ollama
ERROR:src.services.llm_service:LLM connection test failed: litellm.APIConnectionError: OllamaException - [Errno 111] Connection refused
INFO:werkzeug:127.0.0.1 - - [11/Jul/2025 19:25:41] "GET /api/llm/test HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [11/Jul/2025 19:26:12] "GET /api/notes?per_page=1 HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [11/Jul/2025 19:26:12] "GET /api/notes HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [11/Jul/2025 19:26:12] "GET /api/notes?per_page=1 HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [11/Jul/2025 19:26:12] "GET /api/tags HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [11/Jul/2025 19:26:12] "GET /api/notes HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [11/Jul/2025 19:26:12] "GET /api/tags HTTP/1.1" 200 -
[92m19:26:12 - LiteLLM:INFO[0m: utils.py:3225 - 
LiteLLM completion() model= llama3.2; provider = ollama
INFO:LiteLLM:
LiteLLM completion() model= llama3.2; provider = ollama
ERROR:src.services.llm_service:LLM connection test failed: litellm.APIConnectionError: OllamaException - [Errno 111] Connection refused
INFO:werkzeug:127.0.0.1 - - [11/Jul/2025 19:26:12] "GET /api/llm/test HTTP/1.1" 200 -
[92m19:26:12 - LiteLLM:INFO[0m: utils.py:3225 - 
LiteLLM completion() model= llama3.2; provider = ollama
INFO:LiteLLM:
LiteLLM completion() model= llama3.2; provider = ollama
ERROR:src.services.llm_service:LLM connection test failed: litellm.APIConnectionError: OllamaException - [Errno 111] Connection refused
INFO:werkzeug:127.0.0.1 - - [11/Jul/2025 19:26:12] "GET /api/llm/test HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [11/Jul/2025 19:26:33] "GET /api/notes/graph HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [11/Jul/2025 19:26:33] "GET /api/notes/graph HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [11/Jul/2025 19:27:59] "POST /api/notes/import HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [11/Jul/2025 19:28:04] "GET /api/notes/search?q=machine%20learning HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [11/Jul/2025 19:28:10] "GET /api/notes/graph HTTP/1.1" 200 -
