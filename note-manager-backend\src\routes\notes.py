from flask import Blueprint, request, jsonify
from flask_cors import cross_origin
import os
import json
from typing import List, Dict

from src.models.note import Note, Tag, NoteLink, ArchivedFile, db
from src.services.file_service import FileService
from src.services.llm_service import LLMService

notes_bp = Blueprint('notes', __name__)

# Initialize services
llm_service = LLMService()
file_service = FileService(llm_service=llm_service)

@notes_bp.route('/notes', methods=['GET'])
@cross_origin()
def get_notes():
    """
    Get all notes with optional filtering and pagination
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '')
        tag_filter = request.args.get('tag', '')
        
        query = Note.query
        
        # Apply search filter
        if search:
            query = query.filter(
                Note.title.contains(search) | 
                Note.content.contains(search) |
                Note.summary.contains(search)
            )
        
        # Apply tag filter
        if tag_filter:
            query = query.join(Note.tags).filter(Tag.name == tag_filter.lower())
        
        # Paginate results
        notes = query.order_by(Note.updated_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'notes': [note.to_dict() for note in notes.items],
            'total': notes.total,
            'pages': notes.pages,
            'current_page': page,
            'per_page': per_page
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@notes_bp.route('/notes/<int:note_id>', methods=['GET'])
@cross_origin()
def get_note(note_id):
    """
    Get a specific note by ID
    """
    try:
        note = Note.query.get_or_404(note_id)
        return jsonify(note.to_dict())
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@notes_bp.route('/notes/<int:note_id>', methods=['PUT'])
@cross_origin()
def update_note(note_id):
    """
    Update a note's content
    """
    try:
        data = request.get_json()
        if not data or 'content' not in data:
            return jsonify({'error': 'Content is required'}), 400
        
        process_with_llm = data.get('process_with_llm', True)
        
        note = file_service.update_note_content(
            note_id, 
            data['content'], 
            process_with_llm
        )
        
        if not note:
            return jsonify({'error': 'Note not found'}), 404
        
        return jsonify(note.to_dict())
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@notes_bp.route('/notes/<int:note_id>', methods=['DELETE'])
@cross_origin()
def delete_note(note_id):
    """
    Delete a note
    """
    try:
        note = Note.query.get_or_404(note_id)
        
        # Remove file from filesystem
        if os.path.exists(note.file_path):
            os.remove(note.file_path)
        
        # Delete from database
        db.session.delete(note)
        db.session.commit()
        
        return jsonify({'message': 'Note deleted successfully'})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@notes_bp.route('/notes/import', methods=['POST'])
@cross_origin()
def import_files():
    """
    Import files or directories
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'Request data is required'}), 400
        
        file_paths = data.get('file_paths', [])
        directory_paths = data.get('directory_paths', [])
        process_with_llm = data.get('process_with_llm', True)
        recursive = data.get('recursive', True)
        
        imported_notes = []
        
        # Import individual files
        for file_path in file_paths:
            if file_path.strip():  # Only process non-empty paths
                note = file_service.import_file(file_path, process_with_llm)
                if note:
                    imported_notes.append(note.to_dict())
        
        # Import directories
        for directory_path in directory_paths:
            if directory_path.strip():  # Only process non-empty paths
                notes = file_service.import_directory(directory_path, recursive, process_with_llm)
                imported_notes.extend([note.to_dict() for note in notes])
        
        return jsonify({
            'imported_notes': imported_notes,
            'count': len(imported_notes)
        })
        
    except Exception as e:
        print(f"Import error: {e}")
        return jsonify({'error': str(e)}), 500

@notes_bp.route('/notes/search', methods=['GET'])
@cross_origin()
def search_notes():
    """
    Advanced search functionality
    """
    try:
        query_text = request.args.get('q', '')
        search_type = request.args.get('type', 'full_text')  # 'full_text', 'title', 'content', 'tags'
        
        if not query_text:
            return jsonify({'error': 'Search query is required'}), 400
        
        query = Note.query
        
        if search_type == 'title':
            query = query.filter(Note.title.contains(query_text))
        elif search_type == 'content':
            query = query.filter(Note.content.contains(query_text))
        elif search_type == 'tags':
            query = query.join(Note.tags).filter(Tag.name.contains(query_text.lower()))
        else:  # full_text
            query = query.filter(
                Note.title.contains(query_text) | 
                Note.content.contains(query_text) |
                Note.summary.contains(query_text)
            )
        
        notes = query.order_by(Note.updated_at.desc()).all()
        
        return jsonify({
            'notes': [note.to_dict() for note in notes],
            'count': len(notes),
            'query': query_text,
            'search_type': search_type
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@notes_bp.route('/notes/graph', methods=['GET'])
@cross_origin()
def get_graph_data():
    """
    Get graph data for visualization
    """
    try:
        # Get all notes and links
        notes = Note.query.all()
        links = NoteLink.query.all()
        
        # Format nodes
        nodes = []
        for note in notes:
            nodes.append({
                'id': note.id,
                'title': note.title,
                'type': note.file_type,
                'tags': [tag.name for tag in note.tags],
                'topics': json.loads(note.topics) if note.topics else [],
                'summary': note.summary[:100] + '...' if note.summary and len(note.summary) > 100 else note.summary
            })
        
        # Format edges
        edges = []
        for link in links:
            edges.append({
                'id': link.id,
                'source': link.from_note_id,
                'target': link.to_note_id,
                'type': link.link_type
            })
        
        return jsonify({
            'nodes': nodes,
            'edges': edges
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@notes_bp.route('/tags', methods=['GET'])
@cross_origin()
def get_tags():
    """
    Get all tags with note counts
    """
    try:
        tags = db.session.query(Tag).all()
        
        tag_data = []
        for tag in tags:
            tag_data.append({
                'id': tag.id,
                'name': tag.name,
                'color': tag.color,
                'note_count': len(tag.notes)
            })
        
        return jsonify({'tags': tag_data})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@notes_bp.route('/tags/<int:tag_id>', methods=['PUT'])
@cross_origin()
def update_tag(tag_id):
    """
    Update tag properties
    """
    try:
        data = request.get_json()
        tag = Tag.query.get_or_404(tag_id)
        
        if 'name' in data:
            tag.name = data['name'].lower()
        if 'color' in data:
            tag.color = data['color']
        
        db.session.commit()
        return jsonify(tag.to_dict())
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@notes_bp.route('/llm/test', methods=['GET'])
@cross_origin()
def test_llm():
    """
    Test LLM connection
    """
    try:
        is_connected = llm_service.test_connection()
        return jsonify({
            'connected': is_connected,
            'model': llm_service.model_name
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@notes_bp.route('/llm/process/<int:note_id>', methods=['POST'])
@cross_origin()
def process_note_with_llm(note_id):
    """
    Manually trigger LLM processing for a specific note
    """
    try:
        note = Note.query.get_or_404(note_id)
        
        # Extract topics
        topics = llm_service.extract_topics(note.content)
        note.topics = json.dumps(topics)
        
        # Generate summary
        summary = llm_service.summarize_content(note.content)
        note.summary = summary
        
        # Generate tags
        existing_tags = [tag.name for tag in note.tags]
        new_tags = llm_service.generate_tags(note.content, existing_tags)
        
        # Add new tags
        for tag_name in new_tags:
            if tag_name not in existing_tags:
                tag = Tag.query.filter_by(name=tag_name.lower()).first()
                if not tag:
                    tag = Tag(name=tag_name.lower())
                    db.session.add(tag)
                note.tags.append(tag)
        
        db.session.commit()
        
        return jsonify({
            'note': note.to_dict(),
            'message': 'Note processed successfully with LLM'
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@notes_bp.route('/archived', methods=['GET'])
@cross_origin()
def get_archived_files():
    """
    Get list of archived files
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        archived_files = ArchivedFile.query.order_by(
            ArchivedFile.archived_at.desc()
        ).paginate(page=page, per_page=per_page, error_out=False)
        
        return jsonify({
            'archived_files': [af.to_dict() for af in archived_files.items],
            'total': archived_files.total,
            'pages': archived_files.pages,
            'current_page': page,
            'per_page': per_page
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@notes_bp.route('/config/notes-directory', methods=['GET', 'POST'])
@cross_origin()
def notes_directory():
    """
    Get or set the notes directory
    """
    try:
        if request.method == 'GET':
            return jsonify({
                'notes_directory': file_service.get_notes_directory()
            })
        
        elif request.method == 'POST':
            data = request.get_json()
            if not data or 'directory' not in data:
                return jsonify({'error': 'Directory path is required'}), 400
            
            directory_path = data['directory']
            if not os.path.exists(directory_path):
                return jsonify({'error': 'Directory does not exist'}), 400
            
            file_service.set_notes_directory(directory_path)
            
            return jsonify({
                'notes_directory': file_service.get_notes_directory(),
                'message': 'Notes directory updated successfully'
            })
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500

