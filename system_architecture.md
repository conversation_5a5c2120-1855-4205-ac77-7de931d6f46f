# System Architecture Design for Local Note Management Tool

## 1. Overview

This document outlines the proposed system architecture for a local-first note management tool, integrating Large Language Model (LLM) capabilities and graph visualization, with full compatibility for Obsidian notes. The primary goal is to provide a robust, private, and user-friendly environment for managing diverse note formats (.md and .json), extracting insights using LLMs, and visualizing relationships between notes.

## 2. Architectural Components

The system will follow a client-server architecture, albeit entirely local. This design choice allows for clear separation of concerns, modular development, and potential for future extensibility (e.g., network synchronization, though not a current requirement). The main components include:

*   **Frontend (User Interface):** A desktop application providing a rich, interactive user experience for note creation, editing, searching, and visualization.
*   **Backend (Local Server):** A lightweight server responsible for handling data storage, file operations, LLM integrations, and serving data to the frontend.
*   **Database:** A local database system for managing metadata, extracted information, and relationships between notes.
*   **File Storage:** A dedicated directory for storing original note files and their archived versions.
*   **LLM Integration Layer:** A module within the backend responsible for interfacing with various LLMs via LiteLLM.

### 2.1. Frontend

The frontend will be a cross-platform desktop application, likely built using a framework that supports rich UI development and local file system access. Key functionalities will include:

*   **Note Editor:** A Markdown editor with real-time preview, supporting Obsidian-style linking and formatting.
*   **Search Interface:** A powerful search bar supporting full-text search, tag-based filtering, and potentially semantic search.
*   **Graph View:** An interactive visualization of note relationships, allowing users to explore connections, filter nodes, and navigate between related notes.
*   **Import/Export Interface:** Tools for importing .md and .json files, and exporting notes in various formats.
*   **Settings/Configuration:** User preferences for LLM models, API keys (if external LLMs are used), and other application settings.

### 2.2. Backend (Local Server)

The backend will be a local server application, running in the background and communicating with the frontend via a local API (e.g., REST or GraphQL over HTTP). It will be responsible for:

*   **API Endpoints:** Exposing endpoints for the frontend to interact with notes, tags, LLM functions, and graph data.
*   **File System Management:** Reading, writing, and managing .md and .json files in the designated note directory.
*   **Database Interaction:** Storing and retrieving note metadata, LLM-extracted data (topics, summaries, tags), and graph relationships.
*   **LLM Orchestration:** Calling the LLM Integration Layer for processing notes (summarization, tagging, topic extraction).
*   **Archiving:** Managing the separate archive database for original files.

### 2.3. Database

Given the 


requirements for graph visualization and efficient querying of relationships, a **graph database** is a strong candidate for the primary database. Alternatively, a relational database with a separate graph indexing layer could be considered.

#### Option 1: Graph Database (e.g., Neo4j Embedded, Memgraph Embedded)

*   **Pros:**
    *   Native support for graph structures, making queries for relationships highly efficient.
    *   Excellent for visualizing interconnected data.
    *   Aligns well with Obsidian's graph view concept.
*   **Cons:**
    *   May have a steeper learning curve compared to relational databases.
    *   Embedded versions might have limitations on scalability or features compared to server versions.

#### Option 2: Relational Database with Graph Indexing (e.g., SQLite with custom indexing)

*   **Pros:**
    *   Well-understood and widely adopted technology (SQLite is excellent for local applications).
    *   Simpler to set up and manage for a local application.
*   **Cons:**
    *   Graph queries might be less performant or more complex to write compared to a native graph database.
    *   Requires custom logic to build and maintain graph relationships.

**Decision:** For the initial version, we will lean towards a **relational database (SQLite)** for simplicity and ease of local deployment, combined with a custom indexing layer to manage graph relationships. This approach offers a good balance of performance and development effort for a local-first application. If performance becomes a bottleneck for graph operations, a switch to an embedded graph database can be considered in future iterations.

### 2.4. File Storage

File storage will consist of two main parts:

*   **Active Notes Directory:** This will be the primary location where users interact with their `.md` and `.json` files. This directory will be directly compatible with Obsidian vaults, allowing users to open the same notes in both applications.
*   **Archived Files Database:** A separate, immutable database (e.g., SQLite) will store copies of original `.md` and `.json` files upon import. This serves as a version control system and a backup, ensuring that original content is preserved even if notes are modified or deleted in the active directory. Each entry in this archive will include a timestamp and a hash of the file content to ensure integrity.

### 2.5. LLM Integration Layer

This layer will be responsible for all interactions with Large Language Models. It will utilize **LiteLLM** to provide a unified interface to various LLM providers, including local LLMs. Key functionalities include:

*   **Abstraction:** LiteLLM will abstract away the complexities of different LLM APIs, allowing the backend to interact with them uniformly.
*   **Local LLM Prioritization:** The system will prioritize using local LLMs (e.g., via Ollama, Llama.cpp) to ensure 100% local operation and data privacy. Users will have the option to configure external LLM API keys if they choose.
*   **Functionality:**
    *   **Key Topic Extraction:** Using LLMs to identify and extract main themes and topics from note content.
    *   **Tagging:** Automatically generating relevant tags for notes based on their content.
    *   **Summarization:** Condensing long notes or conversation transcripts into shorter summaries.

## 3. Data Flow

1.  **File Import:** User imports `.md` or `.json` files. The backend reads the file content.
2.  **Archiving:** A copy of the original file is stored in the Archived Files Database.
3.  **Metadata Extraction:** The backend extracts metadata (filename, path, creation/modification dates) from the file.
4.  **LLM Processing:** The file content is sent to the LLM Integration Layer for topic extraction, tagging, and summarization. This process will be asynchronous to avoid blocking the UI.
5.  **Database Storage:** Extracted metadata, LLM-generated topics, tags, and summaries are stored in the primary SQLite database. Relationships between notes (e.g., based on links within Markdown or shared topics) are also stored here.
6.  **Frontend Display:** The frontend queries the primary database to display notes, search results, and the graph view.
7.  **User Interaction:** User edits notes in the frontend. Changes are saved back to the active notes directory and potentially trigger re-processing by LLMs if content changes significantly.

## 4. Obsidian Compatibility

To ensure seamless compatibility with Obsidian, the tool will adhere to the following principles:

*   **Markdown Format:** All notes will be stored as standard Markdown files (`.md`).
*   **File Structure:** The active notes directory will mimic Obsidian's vault structure, allowing Obsidian to open and manage the same notes.
*   **Internal Links:** The tool will parse and respect Obsidian's internal link syntax (`[[Note Name]]`). These links will be used to build the graph relationships.
*   **Frontmatter:** Metadata (tags, aliases, etc.) will be stored in YAML frontmatter at the beginning of Markdown files, a common practice in Obsidian.
*   **JSON Handling:** For `.json` files (representing LLM conversations), the tool will parse their content and extract relevant information for indexing and LLM processing, while maintaining the original `.json` file in the active directory and archive.

## 5. Technology Stack (Proposed)

*   **Frontend:** Electron (for cross-platform desktop app) with React/Vue/Angular (for UI framework).
*   **Backend:** Python with FastAPI (for REST API) or Flask.
*   **Database:** SQLite (for primary data and archiving).
*   **LLM Integration:** LiteLLM.
*   **Graph Visualization:** D3.js or similar JavaScript library for interactive graph rendering in the frontend.

This architecture provides a solid foundation for building a powerful, local-first note management tool that meets all the user's requirements. The modular design allows for future enhancements and scalability.

