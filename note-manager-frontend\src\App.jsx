import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  FileText, 
  Network, 
  Settings, 
  Brain, 
  Search,
  Tag,
  Database
} from 'lucide-react'
import NoteList from './components/NoteList'
import NoteViewer from './components/NoteViewer'
import GraphView from './components/GraphView'
import ImportDialog from './components/ImportDialog'
import './App.css'

function App() {
  const [selectedNote, setSelectedNote] = useState(null)
  const [activeTab, setActiveTab] = useState('notes')
  const [stats, setStats] = useState({
    totalNotes: 0,
    totalTags: 0,
    llmConnected: false
  })

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      // Fetch notes count
      const notesResponse = await fetch('/api/notes?per_page=1')
      const notesData = await notesResponse.json()
      
      // Fetch tags count
      const tagsResponse = await fetch('/api/tags')
      const tagsData = await tagsResponse.json()
      
      // Test LLM connection
      const llmResponse = await fetch('/api/llm/test')
      const llmData = await llmResponse.json()

      setStats({
        totalNotes: notesData.total || 0,
        totalTags: tagsData.tags?.length || 0,
        llmConnected: llmData.connected || false
      })
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const handleNoteSelect = (note) => {
    setSelectedNote(note)
    if (activeTab === 'graph') {
      setActiveTab('notes')
    }
  }

  const handleNoteUpdate = (updatedNote) => {
    setSelectedNote(updatedNote)
    fetchStats() // Refresh stats in case tags changed
  }

  const handleImportComplete = () => {
    fetchStats()
    // Refresh note list if we're on the notes tab
    if (activeTab === 'notes') {
      window.location.reload() // Simple refresh for now
    }
  }

  const handleGraphNodeSelect = async (node) => {
    try {
      const response = await fetch(`/api/notes/${node.id}`)
      const noteData = await response.json()
      setSelectedNote(noteData)
      setActiveTab('notes')
    } catch (error) {
      console.error('Error fetching note:', error)
    }
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <Brain className="h-8 w-8 text-primary" />
                <h1 className="text-2xl font-bold">Note Manager</h1>
              </div>
              <Badge variant="outline" className="text-xs">
                Local Knowledge Management
              </Badge>
            </div>
            
            <div className="flex items-center gap-4">
              {/* Stats */}
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <FileText className="h-4 w-4" />
                  <span>{stats.totalNotes} notes</span>
                </div>
                <div className="flex items-center gap-1">
                  <Tag className="h-4 w-4" />
                  <span>{stats.totalTags} tags</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className={`h-2 w-2 rounded-full ${stats.llmConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span>LLM {stats.llmConnected ? 'Connected' : 'Disconnected'}</span>
                </div>
              </div>
              
              <ImportDialog onImportComplete={handleImportComplete} />
              
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-[calc(100vh-140px)]">
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="notes" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Notes
            </TabsTrigger>
            <TabsTrigger value="graph" className="flex items-center gap-2">
              <Network className="h-4 w-4" />
              Graph View
            </TabsTrigger>
          </TabsList>

          <TabsContent value="notes" className="h-full">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full">
              {/* Notes List */}
              <div className="lg:col-span-1">
                <Card className="h-full">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Search className="h-5 w-5" />
                      Notes
                    </CardTitle>
                    <CardDescription>
                      Browse and search your notes
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="h-[calc(100%-120px)] overflow-hidden">
                    <NoteList 
                      onNoteSelect={handleNoteSelect}
                      selectedNoteId={selectedNote?.id}
                    />
                  </CardContent>
                </Card>
              </div>

              {/* Note Viewer */}
              <div className="lg:col-span-2">
                <Card className="h-full">
                  <CardContent className="p-6 h-full">
                    <NoteViewer 
                      note={selectedNote}
                      onNoteUpdate={handleNoteUpdate}
                    />
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="graph" className="h-full">
            <Card className="h-full">
              <CardContent className="p-6 h-full">
                <GraphView onNodeSelect={handleGraphNodeSelect} />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  )
}

export default App
